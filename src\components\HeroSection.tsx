import Navigation from "./Navigation";

export default function HeroSection() {
  return (
    <section className="relative bg-[#FFF5ED]">
      {/* Navigation */}
      <Navigation />
      
      {/* Main content */}
      <div className="pt-24 pb-8 sm:pt-28 lg:pt-32 sm:pb-12 lg:pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-8">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 xl:gap-20 items-center">
            
            {/* Left content */}
            <div className="space-y-3 sm:space-y-4 lg:space-y-5 lg:pr-8 xl:pr-12">
              {/* Main heading */}
              <div className="space-y-2 sm:space-y-2 lg:space-y-3">
                <h1 className="text-3xl sm:text-4xl font-bold text-[#2F2E51] leading-tight lg:leading-[1.05] lg:tracking-[-0.02em] lg:max-w-[40ch] lg:[text-wrap:balance] lg:text-[clamp(36px,4.5vw,56px)]">
                  Dierenverzekering Vergelijken
                </h1>
                <h2 className="text-lg sm:text-xl lg:text-xl font-semibold text-[#2F2E51] mt-4 lg:mt-4 max-w-2xl lg:max-w-3xl xl:max-w-4xl">
                  Goedkoopste Dierenverzekeringen Nederland - Onafhankelijk Vergelijken
                </h2>
                <p className="text-base sm:text-lg text-gray-600 max-w-2xl lg:max-w-3xl xl:max-w-4xl mt-6 lg:mt-6 leading-relaxed">
                  Vergelijk alle 7 Nederlandse dierenverzekeraars op één plek. Vind de beste en goedkoopste
                  dierenverzekering voor jouw hond of kat vanaf €10 per maand. 100% onafhankelijk advies
                  van verzekeringsexperts.
                </p>
              </div>


            </div>

            {/* Right content - Hero image */}
            <div className="relative flex justify-center lg:justify-end mt-6 lg:mt-0 order-first lg:order-none lg:pl-8 xl:pl-12">
              {/* Paw decoration */}
              <div className="absolute top-2 left-4 sm:top-4 sm:left-8 w-6 h-6 sm:w-8 sm:h-8 z-10">
                <svg viewBox="0 0 40 40" className="w-full h-full" fill="none">
                  <g fill="#F2D1B0">
                    <ellipse cx="20" cy="28" rx="6" ry="4"/>
                    <circle cx="12" cy="12" r="3"/>
                    <circle cx="18" cy="9" r="2.5"/>
                    <circle cx="28" cy="12" r="3"/>
                    <circle cx="24" cy="16" r="2.5"/>
                  </g>
                </svg>
              </div>
              
              {/* Main hero image - circular */}
              <div className="w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 rounded-full bg-gradient-to-br from-orange-200 via-orange-300 to-orange-400 p-1 shadow-lg">
                <div className="w-full h-full rounded-full bg-white flex items-center justify-center overflow-hidden">
                  <img 
                    src="/images/hero.webp" 
                    alt="Dierenverzekering vergelijken Nederland - hond en kat verzekeren vanaf €10 per maand" 
                    title="ZoekDierenverzekering.nl - Onafhankelijk vergelijken van alle Nederlandse dierenverzekeraars"
                    className="w-full h-full object-cover rounded-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}