{"name": "newproject", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:report": "playwright show-report", "test:codegen": "playwright codegen http://localhost:3000"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@playwright/test": "^1.54.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "motion": "^12.23.12", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "resend": "^4.6.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}