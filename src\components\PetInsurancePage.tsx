"use client"

import Navigation from "./Navigation";
import Footer from "./Footer";
import BestBeoordeeld from "./BestBeoordeeId";
import FAQSection from "./FAQSection";

interface PetInsurancePageProps {
  petType: string;
  title: string;
}

export default function PetInsurancePage({ petType, title }: PetInsurancePageProps) {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-[#FFF5ED]">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-[#2F2E51] mb-6">
              {title}
            </h1>
            <p className="text-xl text-[#2F2E51] mb-8">
              2 verzekeringen gevonden
            </p>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Vergelijk alle Nederlandse {petType.toLowerCase()}verzekeringen. Vind de beste dekking voor jouw {petType.toLowerCase()} 
              tegen de laagste premie. Onafhankelijk advies van verzekeringsexperts.
            </p>
          </div>
        </div>
      </section>

      {/* Insurance Cards Section */}
      <BestBeoordeeld />

      {/* Why Compare Section for Pet Pages */}
      <section className="py-16 bg-[#FFF5ED]">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 py-16 rounded-2xl px-6 sm:px-8 lg:px-12 xl:px-16 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-100/30 to-indigo-100/30 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-100/20 to-indigo-100/20 rounded-full translate-y-24 -translate-x-24"></div>

            <div className="relative z-10 text-center">
              <h3 className="text-3xl sm:text-4xl font-bold text-[#2F2E51] mb-6">
                Waarom {petType.toLowerCase()}verzekeringen vergelijken?
              </h3>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto mb-12">
                Door {petType.toLowerCase()}verzekeringen te vergelijken bespaar je gemiddeld <span className="font-bold text-green-600">€847 per jaar</span>
                en vind je de beste dekking voor jouw {petType.toLowerCase()}.
              </p>

              <div className="grid sm:grid-cols-3 gap-8">
                <div className="bg-white/90 backdrop-blur-sm p-6 rounded-2xl shadow-xl">
                  <div className="text-4xl font-bold text-green-600 mb-2">€847</div>
                  <div className="text-lg font-semibold text-[#2F2E51] mb-2">Gemiddelde Besparing</div>
                  <div className="text-gray-600">Per jaar door vergelijken</div>
                </div>

                <div className="bg-white/90 backdrop-blur-sm p-6 rounded-2xl shadow-xl">
                  <div className="text-4xl font-bold text-orange-600 mb-2">73%</div>
                  <div className="text-lg font-semibold text-[#2F2E51] mb-2">Betaalt Te Veel</div>
                  <div className="text-gray-600">Zonder te vergelijken</div>
                </div>

                <div className="bg-white/90 backdrop-blur-sm p-6 rounded-2xl shadow-xl">
                  <div className="text-4xl font-bold text-blue-600 mb-2">2min</div>
                  <div className="text-lg font-semibold text-[#2F2E51] mb-2">Tijd Nodig</div>
                  <div className="text-gray-600">Om te vergelijken</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Additional Info Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#2F2E51] mb-4">
              Waarom een {petType.toLowerCase()}verzekering afsluiten?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Een {petType.toLowerCase()} brengt veel vreugde, maar ook onverwachte kosten. Met een goede {petType.toLowerCase()}verzekering 
              ben je verzekerd van de beste zorg voor jouw trouwe viervoeter.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Uitgebreide dekking</h3>
              <p className="text-gray-600">
                Van consult tot operatie, jouw {petType.toLowerCase()} is optimaal verzekerd tegen onverwachte ziektekosten.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Betaalbare premie</h3>
              <p className="text-gray-600">
                Vanaf €10 per maand ben je al verzekerd. Vergelijk en vind de beste prijs-kwaliteitverhouding.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Snelle afhandeling</h3>
              <p className="text-gray-600">
                Declaraties worden snel verwerkt, zodat je je kunt focussen op het welzijn van jouw {petType.toLowerCase()}.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection />

      <Footer />
    </div>
  );
}
