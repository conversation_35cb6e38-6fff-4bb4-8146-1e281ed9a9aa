"use client"

import { useState, useRef, useEffect } from "react";
import Link from "next/link";

interface LogoProps {
  className?: string;
}

function Logo({ className = "" }: LogoProps) {
  return (
    <img
      src="/images/logo.webp"
      alt="Dierenverzekering Logo"
      className={`object-contain max-h-20 sm:max-h-24 lg:max-h-32 ${className}`}
    />
  );
}

export default function Navigation() {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  return (
    <nav className="absolute top-0 left-0 right-0 z-50 bg-[#FFF5ED] h-20 sm:h-24 lg:h-28">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-8 h-full">
        <div className="flex items-center justify-between h-full">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="hover:opacity-80 transition-opacity">
              <Logo />
            </Link>
          </div>

          {/* Navigation Menu - Hidden on mobile, visible on large screens */}
          <div className="hidden lg:flex items-center space-x-8">
            {/* Alle verzekeringen dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center gap-2 text-[#2F2E51] font-semibold text-lg hover:text-opacity-80 transition-colors"
              >
                <span>Alle verzekeringen</span>
                <svg
                  className={`w-3 h-3 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {isDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                  <Link
                    href="/hondenverzekering"
                    className="block px-4 py-3 text-[#2F2E51] hover:bg-gray-50 transition-colors font-medium"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    Hondenverzekering
                  </Link>
                  <Link
                    href="/kattenverzekering"
                    className="block px-4 py-3 text-[#2F2E51] hover:bg-gray-50 transition-colors font-medium"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    Kattenverzekering
                  </Link>
                  <Link
                    href="/konijnverzekering"
                    className="block px-4 py-3 text-[#2F2E51] hover:bg-gray-50 transition-colors font-medium"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    Konijnverzekering
                  </Link>
                  <Link
                    href="/papegaaiverzekering"
                    className="block px-4 py-3 text-[#2F2E51] hover:bg-gray-50 transition-colors font-medium"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    Papegaaiverzekering
                  </Link>
                </div>
              )}
            </div>

            {/* Kennisbank */}
            <Link
              href="/kennisbank"
              className="text-[#2F2E51] font-semibold text-lg hover:text-opacity-80 transition-colors"
            >
              Kennisbank
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button className="lg:hidden flex items-center justify-center p-2" aria-label="Menu">
            <svg className="w-6 h-6 text-[#2F2E51]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </nav>
  );
}