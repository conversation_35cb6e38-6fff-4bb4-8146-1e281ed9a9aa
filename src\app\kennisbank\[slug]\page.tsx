import { notFound } from 'next/navigation';
import { getArticleBySlug, kennisbankArticles } from '../../../data/kennisbankArticles';
import Navigation from '../../../components/Navigation';
import Footer from '../../../components/Footer';
import Image from 'next/image';
import Link from 'next/link';

interface ArticlePageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateStaticParams() {
  return kennisbankArticles.map((article) => ({
    slug: article.slug,
  }));
}

export async function generateMetadata({ params }: ArticlePageProps) {
  const { slug } = await params;
  const article = getArticleBySlug(slug);
  
  if (!article) {
    return {
      title: 'Artikel niet gevonden',
      description: 'Het opgevraagde artikel kon niet worden gevonden.',
    };
  }

  return {
    title: article.metaTitle,
    description: article.metaDescription,
    keywords: article.keywords.join(', '),
    openGraph: {
      title: article.metaTitle,
      description: article.metaDescription,
      type: 'article',
      images: article.image ? [{ url: article.image }] : [],
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const { slug } = await params;
  const article = getArticleBySlug(slug);

  if (!article) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Breadcrumb */}
      <section className="pt-32 pb-6 bg-[#FFF5ED]">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <Link href="/" className="hover:text-[#2F2E51] transition-colors">
              Home
            </Link>
            <span>›</span>
            <Link href="/kennisbank" className="hover:text-[#2F2E51] transition-colors">
              Kennisbank
            </Link>
            <span>›</span>
            <span className="text-[#2F2E51] font-medium">{article.category}</span>
          </nav>
        </div>
      </section>

      {/* Article Header */}
      <section className="py-16 bg-[#FFF5ED]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 xl:gap-20 items-start lg:items-center">
            <div className="lg:pr-8 xl:pr-12">
              <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                {article.category}
              </div>
              <h1 className="text-4xl sm:text-5xl lg:text-5xl xl:text-6xl font-bold text-[#2F2E51] mb-6 leading-tight">
                {article.title}
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed mb-8 max-w-lg">
                {article.description}
              </p>

              {/* Article Meta */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>5 min leestijd</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span>Laatst bijgewerkt: 14 aug 2025</span>
                </div>
              </div>
            </div>

            {/* Article Image */}
            <div className="lg:pl-8 xl:pl-12">
              {article.image && (
                <div className="relative w-full h-80 lg:h-96 xl:h-[28rem] rounded-2xl overflow-hidden shadow-xl">
                  <Image
                    src={article.image}
                    alt={article.title}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-24 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

          {/* Table of Contents */}
          <div className="bg-gray-50 rounded-xl p-6 mb-12">
            <h3 className="text-lg font-bold text-[#2F2E51] mb-4">Inhoudsopgave</h3>
            <ul className="space-y-2">
              <li><a href="#inleiding" className="text-blue-600 hover:text-blue-800 transition-colors">1. Inleiding</a></li>
              <li><a href="#belangrijke-punten" className="text-blue-600 hover:text-blue-800 transition-colors">2. Belangrijke punten</a></li>
              <li><a href="#praktische-tips" className="text-blue-600 hover:text-blue-800 transition-colors">3. Praktische tips</a></li>
              <li><a href="#veelgestelde-vragen" className="text-blue-600 hover:text-blue-800 transition-colors">4. Veelgestelde vragen</a></li>
            </ul>
          </div>

          <div className="prose prose-lg max-w-none">
            {/* Content Template - This would be populated with actual article content */}
            <div className="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    <strong>Let op:</strong> Dit artikel is momenteel in ontwikkeling. De volledige inhoud wordt binnenkort toegevoegd.
                  </p>
                </div>
              </div>
            </div>

            {/* SEO-optimized content structure */}
            <div className="space-y-12">
              <div id="inleiding">
                <h2 className="text-3xl font-bold text-[#2F2E51] mb-6 border-b-2 border-orange-200 pb-3">Inleiding</h2>
                <p className="text-gray-700 leading-relaxed mb-6 text-lg">
                  {article.description} In dit uitgebreide artikel behandelen we alle aspecten van dit onderwerp
                  om je te helpen de beste beslissingen te maken voor je huisdier.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  Als huisdiereigenaar wil je natuurlijk het beste voor je trouwe viervoeter. Dit artikel geeft je
                  alle informatie die je nodig hebt om weloverwogen keuzes te maken.
                </p>
              </div>

              <div id="belangrijke-punten">
                <h2 className="text-3xl font-bold text-[#2F2E51] mb-6 border-b-2 border-orange-200 pb-3">Belangrijke punten</h2>
                <div className="space-y-6">
                  {article.keywords.map((keyword, index) => (
                    <div key={index} className="space-y-3">
                      <h3 className="text-xl font-semibold text-[#2F2E51]">{keyword}</h3>
                      <p className="text-gray-700 leading-relaxed">
                        Alles wat je moet weten over {keyword} en wat dit betekent voor huisdiereigenaren.
                        Dit onderwerp is essentieel voor het maken van de juiste keuzes voor je huisdier.
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              <div id="praktische-tips">
                <h2 className="text-3xl font-bold text-[#2F2E51] mb-6 border-b-2 border-orange-200 pb-3">Praktische tips</h2>
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-[#2F2E51] mb-3">Expert advies</h3>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    Onze experts raden aan om altijd goed onderzoek te doen voordat je belangrijke beslissingen
                    neemt over de zorg voor je huisdier. Vergelijk verschillende opties en vraag advies aan
                    professionals.
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    Het is belangrijk om niet alleen naar de prijs te kijken, maar ook naar de dekking,
                    voorwaarden en de reputatie van de verzekeringsmaatschappij. Neem de tijd om verschillende
                    aanbieders te vergelijken en lees ervaringen van andere huisdiereigenaren.
                  </p>
                </div>
              </div>

              <div id="veelgestelde-vragen">
                <h2 className="text-3xl font-bold text-[#2F2E51] mb-6 border-b-2 border-orange-200 pb-3">Veelgestelde vragen</h2>
                <div className="space-y-6">
                  <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <h3 className="font-semibold text-[#2F2E51] mb-3 text-lg">Wat moet ik weten over {article.keywords[0]}?</h3>
                    <p className="text-gray-700 leading-relaxed">
                      Dit is een van de meest gestelde vragen over dit onderwerp. Het is belangrijk om
                      goed geïnformeerd te zijn voordat je beslissingen neemt. Onze experts hebben uitgebreide
                      informatie samengesteld om je te helpen.
                    </p>
                  </div>

                  <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <h3 className="font-semibold text-[#2F2E51] mb-3 text-lg">Hoe kan ik de beste keuze maken?</h3>
                    <p className="text-gray-700 leading-relaxed">
                      Vergelijk verschillende opties, lees reviews van andere huisdiereigenaren en
                      vraag advies aan experts in het veld. Neem de tijd om alle aspecten te overwegen
                      voordat je een definitieve beslissing neemt.
                    </p>
                  </div>

                  <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <h3 className="font-semibold text-[#2F2E51] mb-3 text-lg">Waar kan ik meer informatie vinden?</h3>
                    <p className="text-gray-700 leading-relaxed">
                      Bekijk onze uitgebreide kennisbank voor meer artikelen over dit onderwerp.
                      Je kunt ook contact opnemen met onze experts voor persoonlijk advies.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-[#2F2E51] to-slate-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Klaar om de beste verzekering te vinden?
          </h2>
          <p className="text-xl text-gray-200 mb-8">
            Vergelijk alle Nederlandse dierenverzekeringen en vind de beste dekking voor jouw huisdier.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="bg-white text-[#2F2E51] px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colors"
            >
              Start Vergelijken
            </Link>
            <Link
              href="/kennisbank"
              className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-[#2F2E51] transition-colors"
            >
              Meer Artikelen
            </Link>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#2F2E51] text-center mb-12">
            Gerelateerde artikelen
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {kennisbankArticles
              .filter(a => a.category === article.category && a.id !== article.id)
              .slice(0, 3)
              .map((relatedArticle) => (
                <Link
                  key={relatedArticle.id}
                  href={`/kennisbank/${relatedArticle.slug}`}
                  className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                >
                  {relatedArticle.image && (
                    <div className="relative w-full h-48">
                      <Image
                        src={relatedArticle.image}
                        alt={relatedArticle.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <div className="p-6">
                    <div className="text-sm text-blue-600 font-medium mb-2">
                      {relatedArticle.category}
                    </div>
                    <h3 className="text-lg font-bold text-[#2F2E51] mb-2 hover:text-blue-600 transition-colors">
                      {relatedArticle.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {relatedArticle.description}
                    </p>
                  </div>
                </Link>
              ))}
          </div>
          
          <div className="text-center mt-12">
            <Link
              href="/kennisbank"
              className="inline-flex items-center gap-2 bg-[#2F2E51] text-white px-8 py-4 rounded-full font-semibold hover:bg-slate-700 transition-all duration-300 hover:scale-105"
            >
              Terug naar Kennisbank
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
