import Image from 'next/image'

export default function PetSelectionSection() {
  return (
    <section className="w-full bg-white py-16">
      <div className="max-w-[1254px] mx-auto px-4">
        <div className="bg-[#2F2E51] rounded-[10px] p-12 relative overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            
            {/* Title */}
            <div className="lg:col-span-1">
              <h2 className="text-[34px] font-bold text-white leading-[1.2] font-figtree">
                Welk huisdier wil je verzekeren?
              </h2>
            </div>

            {/* Dog Option */}
            <div className="lg:col-span-1">
              <a href="/hondenverzekering" className="block">
                <div className="bg-white border border-[#2F2E51] rounded-[10px] overflow-hidden w-full h-[123px] hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="flex h-full">
                    <div className="w-1/2 h-full relative">
                      <Image
                        src="/images/hond-pet-selection-446e94.png"
                        alt="Hond verzekering"
                        width={182}
                        height={121}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="w-1/2 bg-white flex items-center justify-center">
                      <span className="text-[24px] font-bold text-[#2F2E51] font-figtree">
                        Hond
                      </span>
                    </div>
                  </div>
                </div>
              </a>
            </div>

            {/* Cat Option */}
            <div className="lg:col-span-1">
              <a href="/kattenverzekering" className="block">
                <div className="bg-white border border-[#2F2E51] rounded-[10px] overflow-hidden w-full h-[123px] hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="flex h-full">
                    <div className="w-1/2 h-full relative">
                      <Image
                        src="/images/kat-pet-selection-446e94.png"
                        alt="Kat verzekering"
                        width={182}
                        height={121}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="w-1/2 bg-white flex items-center justify-center">
                      <span className="text-[24px] font-bold text-[#2F2E51] font-figtree">
                        Kat
                      </span>
                    </div>
                  </div>
                </div>
              </a>
            </div>
            
          </div>
        </div>
      </div>
    </section>
  )
}