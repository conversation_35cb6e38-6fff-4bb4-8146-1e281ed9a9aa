import Navigation from "../../components/Navigation";
import Footer from "../../components/Footer";
import Image from "next/image";
import Link from "next/link";
import { kennisbankArticles, getArticlesByCategory, getAllCategories } from "../../data/kennisbankArticles";

export const metadata = {
  title: "Kennisbank - Dierenverzekering Informatie | ZoekDierenVerzekering",
  description: "Uitgebreide kennisbank over dierenverzekeringen en huisdierenzorg. Samengesteld met hulp van specialisten en experts.",
};

export default function KennisbankPage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-[#FFF5ED]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 xl:gap-20 items-start lg:items-center">
            <div className="lg:pr-8 xl:pr-12">
              <h1 className="text-4xl sm:text-5xl lg:text-5xl xl:text-6xl font-bold text-[#2F2E51] mb-6">
                Kennisbank
              </h1>
              <p className="text-xl text-gray-600 mb-4 max-w-lg">
                Uitgebreide informatie over dierenverzekeringen en huisdierenzorg.
              </p>
              <p className="text-lg text-gray-600 max-w-lg">
                Samengesteld met hulp van specialisten en experts.
              </p>
            </div>

            <div className="relative lg:pl-8 xl:pl-12">
              <div className="bg-orange-200 rounded-full w-80 h-80 xl:w-96 xl:h-96 mx-auto flex items-center justify-center">
                <Image
                  src="/images/hero.webp"
                  alt="Dierenarts met hond"
                  width={300}
                  height={300}
                  className="rounded-full object-cover xl:w-[350px] xl:h-[350px]"
                />
              </div>
              {/* Paw decoration */}
              <div className="absolute top-4 right-4 text-orange-300 text-4xl">
                🐾
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Articles */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#2F2E51] text-center mb-12">
            Populaire artikelen
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {kennisbankArticles.slice(0, 3).map((article) => (
              <Link
                key={article.id}
                href={`/kennisbank/${article.slug}`}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              >
                <div className="p-6">
                  <div className="text-sm text-blue-600 font-medium mb-2">
                    {article.category}
                  </div>
                  <h3 className="text-lg font-bold text-[#2F2E51] mb-2 hover:text-blue-600 transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {article.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Knowledge Categories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          
          {/* Dynamic Categories */}
          {getAllCategories().map((category) => {
            const categoryArticles = getArticlesByCategory(category);
            const halfLength = Math.ceil(categoryArticles.length / 2);
            const firstHalf = categoryArticles.slice(0, halfLength);
            const secondHalf = categoryArticles.slice(halfLength);
            
            return (
              <div key={category} className="mb-16">
                <h2 className="text-3xl font-bold text-[#2F2E51] mb-8">{category}</h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    {firstHalf.map((article) => (
                      <Link 
                        key={article.id}
                        href={`/kennisbank/${article.slug}`} 
                        className="flex justify-between items-center py-3 border-b border-gray-200 hover:text-[#2F2E51] transition-colors group"
                      >
                        <span className="text-gray-700 group-hover:text-[#2F2E51]">{article.title}</span>
                        <svg className="w-5 h-5 text-gray-400 group-hover:text-[#2F2E51]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    ))}
                  </div>
                  
                  {secondHalf.length > 0 && (
                    <div className="space-y-4">
                      {secondHalf.map((article) => (
                        <Link 
                          key={article.id}
                          href={`/kennisbank/${article.slug}`} 
                          className="flex justify-between items-center py-3 border-b border-gray-200 hover:text-[#2F2E51] transition-colors group"
                        >
                          <span className="text-gray-700 group-hover:text-[#2F2E51]">{article.title}</span>
                          <svg className="w-5 h-5 text-gray-400 group-hover:text-[#2F2E51]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
          
        </div>
      </section>

      <Footer />
    </div>
  );
}
