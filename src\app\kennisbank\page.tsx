import Navigation from "../../components/Navigation";
import Footer from "../../components/Footer";
import Image from "next/image";
import Link from "next/link";
// import { kennisbankArticles, getArticlesByCategory } from "../../data/kennisbankArticles";

export const metadata = {
  title: "Kennisbank - Uitgebreide informatie over dierenverzekeringen",
  description: "Uitgebreide informatie over dierenverzekeringen en huisdierenzorg. Samengesteld met hulp van specialisten en experts.",
};

export default function KennisbankPage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-[#FFF5ED]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 xl:gap-20 items-start lg:items-center">
            <div className="lg:pr-8 xl:pr-12">
              <h1 className="text-4xl sm:text-5xl lg:text-5xl xl:text-6xl font-bold text-[#2F2E51] mb-6">
                Kennisbank
              </h1>
              <p className="text-xl text-gray-600 mb-4 max-w-lg">
                Uitgebreide informatie over dierenverzekeringen en huisdierenzorg.
              </p>
              <p className="text-lg text-gray-600 max-w-lg">
                Samengesteld met hulp van specialisten en experts.
              </p>
            </div>

            <div className="relative lg:pl-8 xl:pl-12">
              <div className="bg-orange-200 rounded-full w-80 h-80 xl:w-96 xl:h-96 mx-auto flex items-center justify-center">
                <Image
                  src="/images/hero.webp"
                  alt="Dierenarts met hond"
                  width={300}
                  height={300}
                  className="rounded-full object-cover xl:w-[350px] xl:h-[350px]"
                />
              </div>
              {/* Paw decoration */}
              <div className="absolute top-4 right-4 text-orange-300 text-4xl">
                🐾
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Articles Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#2F2E51] mb-12">Populaire artikelen</h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <Image
                src="/images/articles/tarieven-hondenbelasting-4bbd69.png"
                alt="Tot welke leeftijd kun je een hondenverzekering afsluiten?"
                width={400}
                height={250}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-bold text-[#2F2E51] mb-2">
                  Tot welke leeftijd kun je een hondenverzekering afsluiten?
                </h3>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <Image
                src="/images/articles/kosten-dierenarts-4bbd69.png"
                alt="Tot welke leeftijd kun je een kattenverzekering afsluiten?"
                width={400}
                height={250}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-bold text-[#2F2E51] mb-2">
                  Tot welke leeftijd kun je een kattenverzekering afsluiten?
                </h3>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <Image
                src="/images/articles/eigen-risico-56586a.png"
                alt="Hoe kies ik een goede dierenarts?"
                width={400}
                height={250}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-bold text-[#2F2E51] mb-2">
                  Hoe kies ik een goede dierenarts?
                </h3>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Knowledge Categories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          
          {/* Verzekeren Section */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-[#2F2E51] mb-8">Verzekeren</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Link href="/kennisbank/is-een-huisdierenverzekering-nodig" className="flex justify-between items-center py-3 border-b border-gray-200 hover:text-[#2F2E51] transition-colors group">
                  <span className="text-gray-700 group-hover:text-[#2F2E51]">Is een huisdierenverzekering nodig?</span>
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-[#2F2E51]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                <Link href="/kennisbank/wat-kost-een-kattenverzekering" className="flex justify-between items-center py-3 border-b border-gray-200 hover:text-[#2F2E51] transition-colors group">
                  <span className="text-gray-700 group-hover:text-[#2F2E51]">Wat kost een kattenverzekering?</span>
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-[#2F2E51]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                <Link href="/kennisbank/hoe-werkt-wachttijd-dierenverzekering" className="flex justify-between items-center py-3 border-b border-gray-200 hover:text-[#2F2E51] transition-colors group">
                  <span className="text-gray-700 group-hover:text-[#2F2E51]">Hoe werkt een wachttijd van een dierenverzekering?</span>
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-[#2F2E51]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                <Link href="/kennisbank/eigen-risico-en-eigen-bijdrage" className="flex justify-between items-center py-3 border-b border-gray-200 hover:text-[#2F2E51] transition-colors group">
                  <span className="text-gray-700 group-hover:text-[#2F2E51]">Eigen risico en eigen bijdrage</span>
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-[#2F2E51]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Kun je voor elk hondenras een hondenverzekering afsluiten?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Tot welke leeftijd kun je een kattenverzekering afsluiten?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Dierenverzekering in België</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Is een konijn verzekeren verstandig?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Wat kost een hondenverzekering?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Wat valt er onder een dierenverzekering?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Is mijn hond of kat meeverzekerd in WA?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Is een hond verzekeren verplicht?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Tot welke leeftijd kun je een hondenverzekering afsluiten?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Kun je een ziek huisdier verzekeren?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Kun je een hulphond verzekeren?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">De premie van mijn dierenverzekering is gestegen, wat nu?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Bij de Dierenarts Section */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-[#2F2E51] mb-8">Bij de Dierenarts</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Link href="/kennisbank/wat-kost-consult-dierenarts" className="flex justify-between items-center py-3 border-b border-gray-200 hover:text-[#2F2E51] transition-colors group">
                  <span className="text-gray-700 group-hover:text-[#2F2E51]">Wat kost een consult bij de dierenarts?</span>
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-[#2F2E51]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Dekt een dierenverzekering dieetvoeding of voedingssupplementen?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Dekt een dierenverzekering fysiotherapie?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Wat als ik &apos;s nachts of in het weekend een dierenarts nodig heb?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Alles over castratie en sterilisatie van hond of kat</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Wat kost een röntgenfoto bij hond of kat?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Dekt een dierenverzekering gedragstherapie?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Dekt een dierenverzekering ontvlooi- of anti-tekenmiddelen?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Alles over het chippen van je hond</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Alles over het chippen van je kat</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Dier en Gezondheid Section */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-[#2F2E51] mb-8">Dier en Gezondheid</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Welke voeding is slecht voor een hond?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Alles over ontwormen van katten</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Mag een hond of kat paracetamol of ibuprofen?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Welke planten zijn giftig voor katten?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Welke voeding is slecht voor katten?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Alles over ontwormen van honden</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Overgewicht bij huisdieren</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Een huisdier grootbrengen Section */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-[#2F2E51] mb-8">Een huisdier grootbrengen</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Wat te doen als je je hond of kat kwijt bent?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Welke hond past bij mij?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Je hond of kat mee naar het buitenland? Dit moet je weten!</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Wat heb je nodig als je een kat neemt?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Hoe kies ik een goede dierenarts?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-gray-700">Wat heb je nodig als je een konijn neemt?</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Onderzoeken Section */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-[#2F2E51] mb-8">Onderzoeken</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Link href="/kennisbank/onderzoek-kosten-dierenarts-2025" className="flex justify-between items-center py-3 border-b border-gray-200 group">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer group-hover:text-blue-800">Onderzoek kosten dierenarts 2025</span>
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer">Chocolade-vergiftigingen bij huisdieren</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer">Tarieven hondenbelasting 2023</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer">Onderzoek kosten dierenarts 2023</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer">Dierenvoeding fors in prijs gestegen</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>

              <div className="space-y-4">
                <Link href="/kennisbank/hondenbelasting-wat-is-het-hoeveel-betalen" className="flex justify-between items-center py-3 border-b border-gray-200 group">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer group-hover:text-blue-800">Hondenbelasting - wat is het, en hoeveel moet je betalen?</span>
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer">Aantal dierenwinkels in Nederland loopt steeds verder terug</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer">Tarieven hondenbelasting 2024</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="text-blue-600 hover:text-blue-800 cursor-pointer">Onderzoek kosten dierenarts 2024</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

        </div>
      </section>

      <Footer />
    </div>
  );
}
