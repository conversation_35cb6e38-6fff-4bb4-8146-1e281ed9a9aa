import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'

export interface MDXContent {
  content: string
  frontmatter: {
    title: string
    metaTitle: string
    metaDescription: string
    canonical?: string
    [key: string]: any
  }
}

export async function getMDXContent(slug: string): Promise<MDXContent | null> {
  try {
    const filePath = path.join(process.cwd(), 'content', 'info', `${slug}.mdx`)
    
    if (!fs.existsSync(filePath)) {
      return null
    }

    const fileContent = fs.readFileSync(filePath, 'utf8')
    const { data: frontmatter, content } = matter(fileContent)

    return {
      content,
      frontmatter: frontmatter as MDXContent['frontmatter']
    }
  } catch (error) {
    console.error(`Error loading MDX content for ${slug}:`, error)
    return null
  }
}

export function getAllMDXSlugs(): string[] {
  try {
    const contentDir = path.join(process.cwd(), 'content', 'info')
    
    if (!fs.existsSync(contentDir)) {
      return []
    }

    const files = fs.readdirSync(contentDir)
    return files
      .filter(file => file.endsWith('.mdx'))
      .map(file => file.replace('.mdx', ''))
  } catch (error) {
    console.error('Error getting MDX slugs:', error)
    return []
  }
}

// Helper function to extract table of contents from markdown content
export function extractTableOfContents(content: string): Array<{ id: string; title: string; level: number }> {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm
  const toc: Array<{ id: string; title: string; level: number }> = []
  let match

  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length
    const title = match[2].trim()
    const id = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    toc.push({ id, title, level })
  }

  return toc
}
