import Image from 'next/image'

export default function BekenVanSection() {
  const mediaLogos = [
    { name: '<PERSON>', src: '/images/hart-van-nederland-56586a.png', alt: '<PERSON> logo' },
    { name: '<PERSON><PERSON><PERSON><PERSON>d', src: '/images/algemeen-dagblad-56586a.png', alt: 'Algemeen Dagblad logo' },
    { name: '<PERSON><PERSON>', src: '/images/een-vandaag-56586a.png', alt: '<PERSON><PERSON> logo' },
    { name: 'De Telegraaf', src: '/images/telegraaf-56586a.png', alt: 'De Telegraaf logo' },
    { name: 'Nu.nl', src: '/images/nu-nl-56586a.png', alt: 'Nu.nl logo' },
    { name: 'Margriet', src: '/images/margriet-56586a.png', alt: '<PERSON><PERSON><PERSON><PERSON> logo' }
  ]

  return (
    <section className="w-full bg-white py-16">
      <div className="max-w-[1284px] mx-auto px-4">
        <h2 className="text-[42px] font-bold text-[#2F2E51] text-center leading-[1.2] mb-16 font-figtree">
          Bekend van
        </h2>
        
        <div className="flex justify-center items-center gap-8 flex-wrap">
          {mediaLogos.map((logo, index) => (
            <div 
              key={index} 
              className="w-[154px] h-[100px] opacity-75 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center"
            >
              <Image
                src={logo.src}
                alt={logo.alt}
                width={154}
                height={100}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}