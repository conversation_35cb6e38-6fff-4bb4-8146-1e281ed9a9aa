"use client"

import Image from "next/image";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ReviewsPopup from "./ReviewsPopup";

interface CoverageItem {
  name: string;
  covered: boolean;
  percentage?: string;
}

interface InsuranceCompany {
  name: string;
  rating: number;
  reviewCount: number;
  features: string[];
  highlighted?: boolean;
  highlightText?: string;
  logo: string;
  dogPrice: string;
  catPrice: string;
  maxCoverage: string;
  ownRisk: string;
  monthlyFee: string;
  coverage: {
    covered: CoverageItem[];
    notCovered: CoverageItem[];
  };
}

const insuranceCompanies: InsuranceCompany[] = [
  {
    name: "Figo",
    rating: 7.9,
    reviewCount: 41,
    features: [
      "Alle rassen en leeftijden welkom"
    ],
    highlighted: true,
    highlightText: "Meest uitgebreide dekking",
    logo: "FIGO",
    dogPrice: "17,25",
    catPrice: "11,41",
    maxCoverage: "€ 3.000,00",
    ownRisk: "Geen",
    monthlyFee: "€ 0,00",
    coverage: {
      covered: [
        { name: "Consult dierenarts", covered: true, percentage: "50%" },
        { name: "Diergeneesmiddelen", covered: true, percentage: "50%" },
        { name: "Operaties", covered: true, percentage: "50%" },
        { name: "Euthanasie", covered: true, percentage: "50%" },
        { name: "Echo- en röntgenonderzoek", covered: true, percentage: "50%" },
        { name: "Identificatiechip", covered: true, percentage: "50%" },
        { name: "Opname/ verblijf kliniek", covered: true, percentage: "50%" },
        { name: "Aandoeningen gewrichten", covered: true, percentage: "50%" },
        { name: "Vaccinaties", covered: true, percentage: "50%" }
      ],
      notCovered: [
        { name: "Castratie/ sterilisatie", covered: false },
        { name: "Dekking buitenland", covered: false }
      ]
    }
  },
  {
    name: "OHRA",
    rating: 8.3,
    reviewCount: 62,
    features: [
      "Basispakket met hoogste vergoeding",
      "Beste afhandeling van declaraties"
    ],
    logo: "OHRA",
    dogPrice: "17,84",
    catPrice: "12,67",
    maxCoverage: "€ 2.500,00",
    ownRisk: "Geen",
    monthlyFee: "€ 0,00",
    coverage: {
      covered: [
        { name: "Consult dierenarts", covered: true, percentage: "80%" },
        { name: "Diergeneesmiddelen", covered: true, percentage: "80%" },
        { name: "Operaties", covered: true, percentage: "80%" },
        { name: "Euthanasie", covered: true, percentage: "80%" },
        { name: "Echo- en röntgenonderzoek", covered: true, percentage: "80%" },
        { name: "Identificatiechip", covered: true, percentage: "80%" },
        { name: "Opname/ verblijf kliniek", covered: true, percentage: "80%" },
        { name: "Aandoeningen gewrichten", covered: true, percentage: "80%" },
        { name: "Vaccinaties", covered: true, percentage: "80%" }
      ],
      notCovered: [
        { name: "Castratie/ sterilisatie", covered: false },
        { name: "Dekking buitenland", covered: false }
      ]
    }
  }
];

function InsuranceCard({ company }: { company: InsuranceCompany }) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showReviews, setShowReviews] = useState(false)

  const getPercentage = () => {
    if (company.logo === 'FIGO') return '50%'
    if (company.logo === 'OHRA') return '80%'
    return '50%'
  }

  const getHighlightColor = () => {
    if (company.logo === 'FIGO') return 'bg-green-100 text-green-700'
    if (company.logo === 'OHRA') return 'bg-blue-100 text-blue-700'
    return 'bg-green-100 text-green-700'
  }

  const getFigoAffiliateLink = () => {
    // Get current page path to determine appropriate affiliate link
    if (typeof window !== 'undefined') {
      const path = window.location.pathname;
      if (path.includes('hondenverzekering')) {
        return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2Fhondenverzekering%2F';
      } else if (path.includes('kattenverzekering')) {
        return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2Fkattenverzekering%2F';
      } else if (path.includes('konijn')) {
        return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2Fkonijnenverzekering%2F';
      } else if (path.includes('papegaai')) {
        return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2Fpapegaaienverzekering%2F';
      }
    }
    // Default to general homepage link
    return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2F';
  }

  const getInsuranceLink = () => {
    if (company.name === 'Figo') {
      return getFigoAffiliateLink();
    }
    // For OHRA and other companies, return placeholder for now
    return '#';
  }

  // Mobile Figma Design Component
  const MobileFigmaDesign = () => {
    const getLogoComponent = () => {
      switch (company.logo) {
        case 'FIGO':
          return (
            <div className="w-[168px] h-[84px] flex items-center justify-center">
              <Image 
                src="/images/logos/figo-logo.svg" 
                alt="Figo Logo" 
                width={168} 
                height={84}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          )
        case 'OHRA':
          return (
            <div className="w-[168px] h-[84px] flex items-center justify-center">
              <Image 
                src="/images/logos/ohra-logo.svg" 
                alt="OHRA Logo" 
                width={168} 
                height={84}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          )
        case 'PETSECUR':
          return (
            <div className="w-[168px] h-[84px] flex items-center justify-center bg-orange-500">
              <Image 
                src="/images/logos/petsecur-logo.png" 
                alt="PetSecur Logo" 
                width={168} 
                height={84}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          )
        case 'UNIVE':
          return (
            <div className="w-[168px] h-[84px] flex items-center justify-center">
              <Image 
                src="/images/logos/unive-logo.svg" 
                alt="Univé Logo" 
                width={168} 
                height={84}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          )
        default:
          return (
            <div className="w-[168px] h-[84px] flex items-center justify-center bg-gray-100">
              <span className="text-lg font-bold text-gray-600">{company.name}</span>
            </div>
          )
      }
    }

    const getHighlightBadge = () => {
      if (company.name === 'Figo' && company.highlighted) {
        return (
          <div className="bg-[#D9F8DF] text-[#2F2E51] px-3 py-2 rounded-full text-base font-bold">
            {company.highlightText}
          </div>
        )
      }
      return null
    }

    return (
      <div className={`relative bg-white rounded-[10px] border ${
        company.highlighted 
          ? 'border-[#4DC166]' 
          : 'border-[#E5E5E5]'
      } overflow-hidden transition-all duration-200`}>
        


        {/* Main card content */}
        <div className="p-[17px] pt-[53px]">
          {/* Logo section */}
          <div className="flex justify-center mb-4">
            {getLogoComponent()}
          </div>

          {/* Stars and rating */}
          <div className="flex items-center justify-center mb-2">
            <div className="flex items-center gap-2 p-1 rounded">
              <div className="w-1 h-1 bg-gradient-to-r from-[#4DC166] to-transparent rounded-full shadow-green-400 shadow-sm"></div>
              <span className="text-[#4DC166] text-2xl">★</span>
              <span className="font-bold text-2xl text-[#4DC166]"> {company.rating}</span>
              <div className="w-1 h-1 bg-gradient-to-r from-[#4DC166] to-transparent rounded-full shadow-green-400 shadow-sm"></div>
            </div>
          </div>

          {/* Review count */}
          <div className="text-center mb-4">
            <button
              onClick={() => setShowReviews(true)}
              className="text-[#2F2E51] text-base underline cursor-pointer hover:text-opacity-80 transition-colors"
            >
              {company.reviewCount} beoordelingen
            </button>
          </div>

          {/* Divider */}
          <div className="w-full h-px bg-[#E5E5E5] mb-6"></div>

          {/* Company name */}
          <div className="mb-4">
            <h3 className="text-[#2F2E51] text-2xl font-bold">{company.name}</h3>
          </div>

          {/* Highlight badge */}
          {getHighlightBadge() && (
            <div className="mb-6">
              {getHighlightBadge()}
            </div>
          )}

          {/* Features list */}
          <div className="mb-6">
            {company.features.map((feature, index) => (
              <div key={index} className="flex items-start gap-3 mb-4">
                <div className="w-[18px] h-[18px] flex-shrink-0 mt-1">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                    <path d="M15 4.5L6.75 12.75L3 9" stroke="#2F2E51" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <span className="text-[#2F2E51] text-base leading-9">{feature}</span>
              </div>
            ))}
          </div>

          {/* Action buttons */}
          <div className="mb-6">
            <a
              href={getInsuranceLink()}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block w-[190px] h-[48px] bg-[#37396E] border border-[#37396E] rounded-full text-white font-bold text-base mb-4 hover:bg-[#2F2E51] transition-colors text-center leading-[48px]"
            >
              Bekijken &gt;
            </a>
            
            <button 
              onClick={() => setIsExpanded(!isExpanded)}
              className="block text-[#2F2E51] text-base underline-none hover:underline"
            >
              Meer informatie
            </button>
          </div>

          {/* Pricing section */}
          <div className="bg-[#FFF5ED] rounded-lg p-4">
            {/* Dog and Cat pricing in tabs */}
            <div className="flex gap-4">
              {/* Dog section */}
              <div className="flex-1">
                <div className="text-[#2F2E51] text-sm mb-1">Hond - Vanaf</div>
                <div className="flex items-baseline">
                  <span className="text-[#2F2E51] text-3xl font-bold">€ {company.dogPrice.split(',')[0]},</span>
                  <span className="text-[#2F2E51] text-[22.5px] font-bold">{company.dogPrice.split(',')[1]}</span>
                </div>
                <div className="text-[#2F2E51] text-sm">p/m</div>
              </div>

              {/* Cat section */}
              <div className="flex-1">
                <div className="text-[#2F2E51] text-sm mb-1">Kat - Vanaf</div>
                <div className="flex items-baseline">
                  <span className="text-[#2F2E51] text-3xl font-bold">€ {company.catPrice.split(',')[0]},</span>
                  <span className="text-[#2F2E51] text-[22.5px] font-bold">{company.catPrice.split(',')[1]}</span>
                </div>
                <div className="text-[#2F2E51] text-sm">p/m</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Desktop Original Design Component  
  const DesktopOriginalDesign = () => (
    <div className={`relative bg-white rounded-xl border-2 ${
      company.highlighted 
        ? 'border-green-500' 
        : 'border-gray-200'
    } overflow-hidden transition-all duration-200`}>
      
      {/* Main card content */}
      <div className="p-4 sm:p-6">
        {/* Header with logo, rating and pricing */}
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 mb-4">
          {/* Left side - Logo and info */}
          <div className="flex items-start gap-4">
            {/* Logo */}
            <div className="w-20 h-16 flex-shrink-0 flex items-center justify-center">
              {company.logo === 'FIGO' && (
                <div className="text-2xl font-bold text-slate-800">
                  FIGO<span className="w-2 h-2 bg-green-500 rounded-full inline-block ml-1 mb-2"></span>
                </div>
              )}
              {company.logo === 'OHRA' && (
                <div className="text-2xl font-bold text-blue-600 border-2 border-blue-600 px-2 py-1 rounded">
                  OHRA
                </div>
              )}
              {company.logo === 'PETSECUR' && (
                <div className="text-xl font-bold text-orange-600">
                  PetSecur
                </div>
              )}
              {company.logo === 'UNIVE' && (
                <div className="text-xl font-bold text-green-600">
                  Univé
                </div>
              )}
            </div>

            {/* Company info */}
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row sm:items-center sm:gap-4 mb-2">
                <h3 className="text-xl font-bold text-slate-800">{company.name} {getPercentage()}</h3>
                {company.highlighted && (
                  <div className={`px-3 py-1 rounded-full text-sm font-semibold ${getHighlightColor()}`}>
                    {company.highlightText}
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-2 mb-3">
                <span className="text-green-600 text-lg">★</span>
                <span className="font-bold text-lg text-slate-800">{company.rating}</span>
                <button
                  onClick={() => setShowReviews(true)}
                  className="text-sm text-gray-600 underline cursor-pointer hover:text-[#2F2E51] transition-colors"
                >
                  {company.reviewCount.toLocaleString('nl-NL')} beoordelingen
                </button>
              </div>

              <div className="text-sm text-slate-700 mb-1">
                <span className="text-green-600">✓</span> {company.features[0]}
              </div>
            </div>
          </div>

          {/* Right side - Pricing */}
          <div className="text-right lg:text-right">
            <div className="text-sm text-gray-600 mb-1">Vanaf</div>
            <div className="text-2xl font-bold text-slate-800">
              € {company.dogPrice}<span className="text-base font-normal text-gray-600">/p/m</span>
            </div>
          </div>
        </div>

        {/* Summary info */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-lg font-bold text-slate-800">{company.maxCoverage}</div>
            <div className="text-sm text-gray-600">Verzekerd bedrag</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-slate-800">{company.ownRisk}</div>
            <div className="text-sm text-gray-600">Eigen risico</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-slate-800">{company.monthlyFee}</div>
            <div className="text-sm text-gray-600">Eenmalige kosten</div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-3 mb-4">
          <a
            href={getInsuranceLink()}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 bg-gradient-to-r from-[#2F2E51] to-slate-700 text-white px-6 py-3 rounded-full font-semibold hover:from-slate-700 hover:to-[#2F2E51] shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 text-center"
          >
            Bekijken →
          </a>
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center justify-center gap-2 text-[#2F2E51] font-semibold hover:text-slate-700 px-4 py-3 rounded-full hover:bg-[#2F2E51]/5 transition-all duration-300"
          >
            Meer details
            <motion.svg 
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.25, ease: "easeInOut" }}
              className="w-4 h-4" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </motion.svg>
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="relative">
      {/* Show Figma design on mobile only */}
      <div className="block sm:hidden">
        <MobileFigmaDesign />
      </div>
      
      {/* Show original design on desktop */}
      <div className="hidden sm:block">
        <DesktopOriginalDesign />
      </div>

      {/* Expandable content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className={`
              ${company.highlighted ? 'border-[#4DC166]' : 'border-gray-200'} 
              border-t 
              sm:bg-gray-50 
              bg-white 
              sm:rounded-none
              rounded-b-[10px]
              overflow-hidden
            `}
          >
            <div className="p-4 sm:p-6">
              <h4 className="text-lg sm:text-xl font-bold text-[#2F2E51] mb-4 sm:mb-6">
                Dekking- en vergoedingsoverzicht
              </h4>
              
              <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                {/* Verzekerd section */}
                <div>
                  <h5 className="text-base sm:text-lg font-semibold text-[#2F2E51] mb-3 sm:mb-4">
                    Verzekerd:
                  </h5>
                  <div className="space-y-2 sm:space-y-3">
                    {company.coverage.covered.map((item, index) => (
                      <div 
                        key={index} 
                        className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 shadow-sm"
                      >
                        <div className="flex items-center gap-2 sm:gap-3">
                          <div className="w-5 h-5 sm:w-6 sm:h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-green-600 text-xs sm:text-sm">✓</span>
                          </div>
                          <span className="text-[#2F2E51] text-sm sm:text-base leading-tight">
                            {item.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 sm:gap-2">
                          <span className="text-xs sm:text-sm font-semibold text-[#2F2E51] whitespace-nowrap">
                            {item.percentage}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Niet verzekerd section */}
                <div>
                  <h5 className="text-base sm:text-lg font-semibold text-[#2F2E51] mb-3 sm:mb-4">
                    Niet verzekerd:
                  </h5>
                  <div className="space-y-2 sm:space-y-3">
                    {company.coverage.notCovered.map((item, index) => (
                      <div 
                        key={index} 
                        className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 shadow-sm"
                      >
                        <div className="flex items-center gap-2 sm:gap-3">
                          <div className="w-5 h-5 sm:w-6 sm:h-6 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-gray-400 text-xs sm:text-sm">✗</span>
                          </div>
                          <span className="text-[#2F2E51] text-sm sm:text-base leading-tight">
                            {item.name}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Reviews Popup */}
      <ReviewsPopup
        isOpen={showReviews}
        onClose={() => setShowReviews(false)}
        companyName={company.name}
        overallRating={company.rating}
        totalReviews={company.reviewCount}
      />
    </div>
  );
}

export default function BestBeoordeeld() {
  return (
    <section className="pt-16 sm:pt-20 lg:pt-24 pb-12 bg-[#FFF5ED]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <span className="text-green-500 text-lg">★ ★</span>
            <h2 className="text-3xl font-bold text-[#2F2E51]">
              Nederlandse Dierenverzekeraars - Best Beoordeeld
            </h2>
            <span className="text-green-500 text-lg">★ ★</span>
          </div>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Vergelijk alle grote Nederlandse dierenverzekeraars op dekking, premie en klanttevredenheid. 
            Onze experts beoordelen elke verzekeraar op basis van prijs-kwaliteit verhouding, 
            dekking en snelheid van schadeafhandeling.
          </p>
        </div>



        {/* Insurance Cards */}
        <div className="space-y-4 mb-8">
          {insuranceCompanies.map((company, index) => (
            <InsuranceCard key={index} company={company} />
          ))}
        </div>

        {/* Bottom buttons */}
        <div className="flex flex-col sm:flex-row justify-center gap-6 mb-16">
          <a
            href="/hondenverzekering"
            className="group relative overflow-hidden bg-gradient-to-r from-[#2F2E51] to-[#3d3c6b] text-white px-10 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out min-w-[140px] border-2 border-transparent hover:border-white/20 text-center"
          >
            <span className="relative z-10">Hond</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#3d3c6b] to-[#2F2E51] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </a>
          <a
            href="/kattenverzekering"
            className="group relative overflow-hidden bg-gradient-to-r from-[#2F2E51] to-[#3d3c6b] text-white px-10 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out min-w-[140px] border-2 border-transparent hover:border-white/20 text-center"
          >
            <span className="relative z-10">Kat</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#3d3c6b] to-[#2F2E51] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </a>
        </div>

        {/* Why Compare Section */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 py-16 mb-8 rounded-2xl px-6 sm:px-8 lg:px-12 xl:px-16 relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-100/30 to-indigo-100/30 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-100/20 to-indigo-100/20 rounded-full translate-y-24 -translate-x-24"></div>

          <div className="relative z-10">
            <div className="text-center mb-12">
              <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full mb-4">
                <span className="text-green-500 text-lg">💰</span>
                <span className="text-sm font-semibold text-[#2F2E51]">Bewezen Besparingen</span>
              </div>
              <h3 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#2F2E51] mb-6">
                Waarom vergelijken altijd loont
              </h3>
              <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
                Door slim vergelijken besparen Nederlandse huisdiereigenaren gemiddeld <span className="font-bold text-green-600">€847 per jaar</span>
                op hun dierenverzekering. Ontdek waarom vergelijken altijd loont.
              </p>
            </div>

            {/* Enhanced Stats grid */}
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {/* Stat 1 - Enhanced */}
              <div className="text-center bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/50">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span className="text-3xl font-bold text-white">€847</span>
                </div>
                <h4 className="text-xl font-bold text-[#2F2E51] mb-3">Gemiddelde Jaarlijkse Besparing</h4>
                <p className="text-gray-600 leading-relaxed">
                  Door dierenverzekeringen te vergelijken besparen klanten gemiddeld €847 per jaar
                  op hun premie zonder afbreuk te doen aan de dekking.
                </p>
                <div className="mt-4 text-sm text-green-600 font-semibold">
                  ✓ Gebaseerd op 10.000+ vergelijkingen
                </div>
              </div>

              {/* Stat 2 - Enhanced */}
              <div className="text-center bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/50">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span className="text-3xl font-bold text-white">73%</span>
                </div>
                <h4 className="text-xl font-bold text-[#2F2E51] mb-3">Betaalt Te Veel</h4>
                <p className="text-gray-600 leading-relaxed">
                  Van alle Nederlandse huisdiereigenaren betaalt 73% te veel voor hun dierenverzekering
                  omdat ze nooit hebben vergeleken.
                </p>
                <div className="mt-4 text-sm text-orange-600 font-semibold">
                  ✓ Onderzoek onder 5.000 huisdiereigenaren
                </div>
              </div>

              {/* Stat 3 - Enhanced */}
              <div className="text-center bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/50">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span className="text-3xl font-bold text-white">2min</span>
                </div>
                <h4 className="text-xl font-bold text-[#2F2E51] mb-3">Tijd Nodig om te Vergelijken</h4>
                <p className="text-gray-600 leading-relaxed">
                  In slechts 2 minuten vind je de beste dierenverzekering die perfect past bij
                  jouw huisdier en budget.
                </p>
                <div className="mt-4 text-sm text-blue-600 font-semibold">
                  ✓ Sneller dan een kopje koffie zetten
                </div>
              </div>
            </div>

            {/* Enhanced CTA Section */}
            <div className="text-center">
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50">
                <h4 className="text-2xl font-bold text-[#2F2E51] mb-4">
                  Start nu met vergelijken en bespaar direct
                </h4>
                <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
                  Vind in 2 minuten de beste dierenverzekering voor jouw huisdier.
                  Onafhankelijk vergelijken van alle Nederlandse aanbieders.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/"
                    className="bg-gradient-to-r from-[#2F2E51] to-slate-700 text-white px-8 py-4 rounded-full font-semibold hover:from-slate-700 hover:to-[#2F2E51] shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-center"
                  >
                    Start Vergelijken
                  </a>
                  <a
                    href="/kennisbank"
                    className="bg-white text-[#2F2E51] border-2 border-[#2F2E51] px-8 py-4 rounded-full font-semibold hover:bg-[#2F2E51] hover:text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-center"
                  >
                    Gratis Expert Advies
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Specific comparison examples */}
        <div className="grid sm:grid-cols-2 gap-6">
            {/* Dog comparison */}
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <h4 className="text-xl font-bold text-[#2F2E51]">
                  Hondenverzekering vergelijken - beste verzekering voor honden vanaf €15 per maand
                </h4>
              </div>
              <p className="text-gray-600 mb-4">
                Vergelijk alle Nederlandse hondenverzekeringen. Vind de beste dekking voor jouw hond 
                tegen de laagste premie.
              </p>
              <a 
                href="/hondenverzekering" 
                className="inline-flex items-center gap-2 text-[#2F2E51] font-semibold hover:text-slate-700"
              >
                Hondenverzekering vergelijken
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>

            {/* Cat comparison */}
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <h4 className="text-xl font-bold text-[#2F2E51]">
                  Kattenverzekering vergelijken - beste verzekering voor katten vanaf €10 per maand
                </h4>
              </div>
              <p className="text-gray-600 mb-4">
                Vergelijk alle Nederlandse kattenverzekeringen. Vind de beste dekking voor jouw kat 
                tegen de laagste premie.
              </p>
              <a 
                href="/kattenverzekering" 
                className="inline-flex items-center gap-2 text-[#2F2E51] font-semibold hover:text-slate-700"
              >
                Kattenverzekering vergelijken
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>

        {/* Alle verzekeraars section */}
        <div className="bg-gray-50 py-12 rounded-lg">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-[#2F2E51] mb-4">
              Alle Nederlandse Dierenverzekeraars Vergelijken
            </h3>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Bekijk ons complete overzicht met alle aanbieders van huisdierenverzekeringen in Nederland. 
              Vergelijk premies, dekking en voorwaarden van OHRA, PetSecur, Figo, Univé, InShared, ASR en 
              Dierenverzekering.nl om jouw beste keuze te maken.
            </p>
          </div>

          {/* Insurance company logos grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8 max-w-4xl mx-auto">
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-orange-500 mb-2">PetSecur</div>
              <div className="text-xs sm:text-sm text-gray-600">PetSecur</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-800 mb-2">FIGO</div>
              <div className="text-xs sm:text-sm text-gray-600">Figo</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-blue-600 border-2 border-blue-600 px-2 py-1 rounded mb-2">OHRA</div>
              <div className="text-xs sm:text-sm text-gray-600">OHRA</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-pink-500 mb-2">inshared</div>
              <div className="text-xs sm:text-sm text-gray-600">Inshared</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-green-600 mb-2">univé</div>
              <div className="text-xs sm:text-sm text-gray-600">Univé</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-sm sm:text-lg lg:text-xl font-bold text-slate-800 mb-2 text-center">DIERENVERZEKERING</div>
              <div className="text-xs sm:text-sm text-gray-600 text-center">Dierenverzekering.nl</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl text-gray-600 mb-2">★★★</div>
              <div className="text-xs sm:text-sm text-gray-600">AAL</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}