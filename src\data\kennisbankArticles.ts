export interface Article {
  id: string;
  slug: string;
  title: string;
  description: string;
  category: string;
  image?: string;
  content?: string;
  keywords: string[];
  metaTitle: string;
  metaDescription: string;
}

export const kennisbankArticles: Article[] = [
  // Verzekeren Category
  {
    id: "1",
    slug: "is-een-huisdierenverzekering-nodig",
    title: "Is een huisdierenverzekering nodig?",
    description: "Ontdek waarom een huisdierenverzekering essentieel is voor de gezondheid van je huisdier en je financiële zekerheid.",
    category: "Verzekeren",
    image: "/images/articles/tarieven-hondenbelasting-4bbd69.png",
    keywords: ["huisdierenverzekering", "dierenverzekering nodig", "huisdier verzekeren", "dierenarts kosten"],
    metaTitle: "Is een huisdierenverzekering nodig? | Complete gids 2025",
    metaDescription: "Ontdek waarom een huisdierenverzekering essentieel is. Lees over kosten, dekking en voordelen van het verzekeren van je huisdier."
  },
  {
    id: "2",
    slug: "wat-kost-een-kattenverzekering",
    title: "Wat kost een kattenverzekering?",
    description: "Alles over de kosten van een kattenverzekering in 2025. Vergelijk premies en vind de beste prijs-kwaliteitverhouding.",
    category: "Verzekeren",
    image: "/images/articles/kosten-dierenarts-4bbd69.png",
    keywords: ["kattenverzekering kosten", "premie kattenverzekering", "kat verzekeren prijs"],
    metaTitle: "Wat kost een kattenverzekering in 2025? | Prijzen vergelijken",
    metaDescription: "Ontdek wat een kattenverzekering kost in 2025. Vergelijk premies van alle aanbieders en vind de beste deal voor je kat."
  },
  {
    id: "3",
    slug: "hoe-werkt-wachttijd-dierenverzekering",
    title: "Hoe werkt een wachttijd van een dierenverzekering?",
    description: "Uitleg over wachttijden bij dierenverzekeringen en wat dit betekent voor de dekking van je huisdier.",
    category: "Verzekeren",
    image: "/images/articles/eigen-risico-56586a.png",
    keywords: ["wachttijd dierenverzekering", "wachtperiode huisdierenverzekering", "dekking na afsluiten"],
    metaTitle: "Wachttijd dierenverzekering uitgelegd | Wat moet je weten?",
    metaDescription: "Alles over wachttijden bij dierenverzekeringen. Wanneer ben je gedekt en wat betekent de wachtperiode voor je huisdier?"
  },
  {
    id: "4",
    slug: "eigen-risico-en-eigen-bijdrage",
    title: "Eigen risico en eigen bijdrage",
    description: "Het verschil tussen eigen risico en eigen bijdrage bij dierenverzekeringen uitgelegd.",
    category: "Verzekeren",
    image: "/images/articles/eigen-risico-56586a.png",
    keywords: ["eigen risico dierenverzekering", "eigen bijdrage huisdier", "zelfrisico dierenarts"],
    metaTitle: "Eigen risico vs eigen bijdrage dierenverzekering | Uitleg",
    metaDescription: "Wat is het verschil tussen eigen risico en eigen bijdrage bij dierenverzekeringen? Lees hier alles over de kosten."
  },

  // Bij de Dierenarts Category
  {
    id: "5",
    slug: "wat-kost-consult-dierenarts",
    title: "Wat kost een consult bij de dierenarts?",
    description: "Overzicht van dierenarts tarieven in 2025 en wat je kunt verwachten te betalen voor verschillende behandelingen.",
    category: "Bij de Dierenarts",
    image: "/images/articles/kosten-dierenarts-4bbd69.png",
    keywords: ["dierenarts kosten", "consult dierenarts prijs", "dierenarts tarieven 2025"],
    metaTitle: "Wat kost een consult bij de dierenarts in 2025?",
    metaDescription: "Ontdek wat een dierenarts consult kost in 2025. Overzicht van alle tarieven en kosten voor verschillende behandelingen."
  },
  {
    id: "6",
    slug: "wat-kost-rontgenfoto-hond-kat",
    title: "Wat kost een röntgenfoto bij hond of kat?",
    description: "Alles over de kosten van röntgenonderzoek bij huisdieren en wanneer dit nodig is.",
    category: "Bij de Dierenarts",
    image: "/images/articles/tarieven-hondenbelasting-4bbd69.png",
    keywords: ["röntgen hond kosten", "röntgen kat prijs", "diagnostiek huisdier"],
    metaTitle: "Röntgen kosten hond en kat | Prijzen 2025",
    metaDescription: "Wat kost een röntgenfoto voor je hond of kat? Lees over de kosten en wanneer röntgenonderzoek nodig is."
  },

  // Dier en Gezondheid Category
  {
    id: "7",
    slug: "welke-voeding-slecht-voor-hond",
    title: "Welke voeding is slecht voor een hond?",
    description: "Overzicht van voedingsmiddelen die gevaarlijk zijn voor honden en wat je moet vermijden.",
    category: "Dier en Gezondheid",
    image: "/images/articles/kosten-dierenarts-4bbd69.png",
    keywords: ["giftige voeding hond", "gevaarlijk eten hond", "chocolade hond", "voeding vermijden hond"],
    metaTitle: "Welke voeding is gevaarlijk voor honden? | Giftige voedingsmiddelen",
    metaDescription: "Lees welke voedingsmiddelen gevaarlijk zijn voor honden. Overzicht van giftige voeding die je moet vermijden."
  },
  {
    id: "8",
    slug: "overgewicht-bij-huisdieren",
    title: "Overgewicht bij huisdieren",
    description: "Herken en voorkom overgewicht bij je huisdier. Tips voor een gezond gewicht en goede voeding.",
    category: "Dier en Gezondheid",
    image: "/images/articles/eigen-risico-56586a.png",
    keywords: ["overgewicht hond", "overgewicht kat", "huisdier afvallen", "gezond gewicht huisdier"],
    metaTitle: "Overgewicht bij huisdieren herkennen en voorkomen",
    metaDescription: "Heeft je huisdier overgewicht? Lees hoe je overgewicht herkent, voorkomt en wat je kunt doen om je huisdier gezond te houden."
  },

  // Een huisdier grootbrengen Category
  {
    id: "9",
    slug: "wat-te-doen-huisdier-kwijt",
    title: "Wat te doen als je je hond of kat kwijt bent?",
    description: "Stap-voor-stap plan wanneer je huisdier vermist is en hoe je de kans op terugvinden vergroot.",
    category: "Een huisdier grootbrengen",
    image: "/images/articles/tarieven-hondenbelasting-4bbd69.png",
    keywords: ["hond kwijt", "kat vermist", "huisdier zoeken", "vermist huisdier"],
    metaTitle: "Huisdier kwijt? Dit moet je direct doen | Stappenplan",
    metaDescription: "Is je hond of kat kwijt? Volg dit stappenplan om je vermiste huisdier snel terug te vinden. Tips van experts."
  },
  {
    id: "10",
    slug: "hoe-kies-ik-goede-dierenarts",
    title: "Hoe kies ik een goede dierenarts?",
    description: "Tips voor het kiezen van de juiste dierenarts voor je huisdier. Waar moet je op letten?",
    category: "Een huisdier grootbrengen",
    image: "/images/articles/kosten-dierenarts-4bbd69.png",
    keywords: ["dierenarts kiezen", "goede dierenarts vinden", "dierenartspraktijk", "huisdier zorg"],
    metaTitle: "Hoe kies je een goede dierenarts? | Tips en advies",
    metaDescription: "Zoek je een goede dierenarts? Lees onze tips voor het kiezen van de juiste dierenartspraktijk voor je huisdier."
  },

  // Onderzoeken Category
  {
    id: "11",
    slug: "onderzoek-kosten-dierenarts-2025",
    title: "Onderzoek kosten dierenarts 2025",
    description: "Uitgebreid onderzoek naar de kosten van dierenartsen in Nederland in 2025.",
    category: "Onderzoeken",
    image: "/images/articles/eigen-risico-56586a.png",
    keywords: ["dierenarts kosten onderzoek", "tarieven dierenarts 2025", "kosten studie dierenarts"],
    metaTitle: "Onderzoek: Kosten dierenarts in Nederland 2025",
    metaDescription: "Uitgebreid onderzoek naar dierenarts kosten in 2025. Wat betaal je gemiddeld voor verschillende behandelingen?"
  },
  {
    id: "12",
    slug: "hondenbelasting-wat-is-het-hoeveel-betalen",
    title: "Hondenbelasting - wat is het, en hoeveel moet je betalen?",
    description: "Alles over hondenbelasting in Nederland: tarieven per gemeente, vrijstellingen en hoe te betalen.",
    category: "Onderzoeken",
    image: "/images/articles/tarieven-hondenbelasting-4bbd69.png",
    keywords: ["hondenbelasting", "hondenbelasting tarieven", "gemeente hondenbelasting", "hond aanmelden"],
    metaTitle: "Hondenbelasting Nederland 2025 | Tarieven per gemeente",
    metaDescription: "Alles over hondenbelasting in Nederland. Bekijk tarieven per gemeente, vrijstellingen en hoe je je hond moet aanmelden."
  }
];

export function getArticleBySlug(slug: string): Article | undefined {
  return kennisbankArticles.find(article => article.slug === slug);
}

export function getArticlesByCategory(category: string): Article[] {
  return kennisbankArticles.filter(article => article.category === category);
}

export function getAllCategories(): string[] {
  const categories = kennisbankArticles.map(article => article.category);
  return [...new Set(categories)];
}
